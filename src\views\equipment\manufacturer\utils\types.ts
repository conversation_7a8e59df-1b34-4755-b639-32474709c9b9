import type { EquipmentManufacturerRMDto } from "@/api/model/dto";

// ===== API 数据类型（直接映射 API 返回结构） =====
export type EquipmentManufacturerListItem =
  EquipmentManufacturerRMDto["EquipmentManufacturerQueryController/EQUIPMENT_MANUFACTURER_LIST"];
export type EquipmentManufacturerOption =
  EquipmentManufacturerRMDto["EquipmentManufacturerQueryController/EQUIPMENT_MANUFACTURER_OPTIONS"];

// ===== 业务数据类型（用于表单和业务逻辑） =====
// 搜索条件接口（只包含搜索参数，不包含选项数据）
interface EquipmentManufacturerSearch {
  readonly manufacturerName?: string;
  readonly manufacturerId?: number; // 按制造商ID搜索
  readonly status?: "active" | "inactive"; // 按状态搜索
  readonly sortOrder?: number; // 按排序搜索
  readonly createTimeStart?: string; // 创建时间范围开始
  readonly createTimeEnd?: string; // 创建时间范围结束
}

// 搜索表单的选项数据接口（用于下拉框等组件的数据源）
interface EquipmentManufacturerSearchOptions {
  readonly manufacturerOptions: EquipmentManufacturerOption[]; // 制造商下拉选项
  readonly statusOptions: Array<{ label: string; value: string }>; // 状态选项
  readonly sortOptions: Array<{ label: string; value: string }>; // 排序选项
}

// 表单数据接口（用于新增/编辑）
interface EquipmentManufacturerForm {
  readonly equipmentManufacturerId?: number;
  readonly manufacturerName?: string;
  readonly remark?: string | undefined;
  readonly sortOrder?: number;
}

export type {
  EquipmentManufacturerSearch,
  EquipmentManufacturerSearchOptions,
  EquipmentManufacturerForm
};
